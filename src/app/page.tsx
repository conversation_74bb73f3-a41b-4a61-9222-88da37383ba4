"use client"

import { useState, useEffect, useRef } from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"

import { DiamondSection } from "@/components/diamond-section"
import { InteractiveImageSection } from "@/components/interactive-image-section"
import { ProductOverviewSection } from "@/components/product-overview-section"
import { FeaturesSection } from "@/components/features-section"
import { HowItWorksSection } from "@/components/how-it-works-section"
import { ThreePillarsSection } from "@/components/three-pillars-section"
import { TeamSection } from "@/components/team-section"
import { SparklesWaitlistSection } from "@/components/sparkles-waitlist-section"
// import { ScrollIndicator } from "@/components/ScrollIndicator"
import { getSectionContent, HeroContent, VideoHeroContent } from "@/lib/content"
import { heroContainer, heroText, heroButton, getAnimationVariants } from "@/lib/animations"
// import { useSafeScrollTransition } from "@/lib/hooks/useSafeScrollTransition"

export default function VideoLandingPage() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)
  const videoRefs = useRef<(HTMLVideoElement | null)[]>([])

  // Get content from externalized sources
  const heroContent = getSectionContent(1) as HeroContent
  const videoHeroContent = getSectionContent(10) as VideoHeroContent
  const videoData = videoHeroContent.videos.slice(0, 2) // Only show first 2 videos

  // Safe scroll transition system
  // Removed scroll transition animations

  // Removed development helper

  // Auto-advance slides
  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % videoData.length)
    }, 5000) // Change slide every 5 seconds

    return () => clearInterval(interval)
  }, [isAutoPlaying, videoData.length])

  // Control video playback when slide changes
  useEffect(() => {
    videoRefs.current.forEach((video, index) => {
      if (video) {
        if (index === currentSlide && videoData[index].isVideo) {
          // Play the current video
          video.currentTime = 0 // Restart from beginning
          video.play().catch((error) => {
            console.log("Video play failed:", error)
          })
        } else {
          // Pause all other videos
          video.pause()
        }
      }
    })
  }, [currentSlide, videoData])

  const handleDotClick = (index: number) => {
    setCurrentSlide(index)
    setIsAutoPlaying(false)
    // Resume auto-play after 10 seconds
    setTimeout(() => setIsAutoPlaying(true), 10000)
  }

  const currentVideo = videoData[currentSlide]

  return (
    <div className="relative w-full">
      {/* Video Hero Section - Full Screen */}
      <motion.section
        className="relative h-screen w-full overflow-hidden"
        style={{
          backgroundColor: 'var(--color-black)'
        }}
      >
        {/* Background Videos */}
        <div className="absolute inset-0">
          {videoData.map((video, index) => (
            <div
              key={video.id}
              className={`absolute inset-0 transition-opacity ${
                index === currentSlide ? "opacity-100" : "opacity-0"
              }`}
              style={{ transitionDuration: 'var(--duration-1000)' }}
            >
              {/* Render actual video element or background image */}
              {video.isVideo ? (
                <video
                  ref={(el) => {
                    videoRefs.current[index] = el
                  }}
                  className="h-full w-full object-cover"
                  muted
                  loop
                  playsInline
                  preload="metadata"
                >
                  <source src={video.videoUrl} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              ) : (
                <div
                  className="h-full w-full bg-cover bg-center bg-no-repeat"
                  style={{
                    backgroundImage: `url('${video.videoUrl}')`,
                  }}
                />
              )}
              {/* Video overlay for better text readability */}
              <div className="absolute inset-0 video-overlay" />
            </div>
          ))}
        </div>

        {/* Content Overlay */}
        <div className="relative z-10 flex h-full items-center justify-between px-8 md:px-16 lg:px-24">
          {/* Left Content */}
          <motion.div
            className="max-w-2xl"
            style={{ color: 'var(--color-white)' }}
            variants={getAnimationVariants(heroContainer)}
            initial="hidden"
            animate="visible"
          >
            <motion.h1
              className="mb-4 text-4xl font-bold leading-tight md:text-5xl lg:text-6xl"
              variants={getAnimationVariants(heroText)}
            >
              {heroContent.headline}
            </motion.h1>
            <motion.p
              className="mb-8 text-lg leading-relaxed md:text-xl lg:text-2xl"
              style={{ color: 'var(--color-gray-200)' }}
              variants={getAnimationVariants(heroText)}
            >
              {heroContent.subheadline}
            </motion.p>
            <motion.div variants={getAnimationVariants(heroButton)}>
              <Button size="lg" className="btn-cta">
                {heroContent.ctaButton}
              </Button>
            </motion.div>
            {heroContent.additionalText && (
              <motion.p
                className="mt-4 text-sm"
                style={{ color: 'var(--color-gray-300)' }}
                variants={getAnimationVariants(heroText)}
              >
                {heroContent.additionalText}
              </motion.p>
            )}
          </motion.div>

          {/* Right Content - Statistics Display */}
          <motion.div
            className="hidden md:block"
            style={{ color: 'var(--color-white)' }}
            variants={getAnimationVariants(heroContainer)}
            initial="hidden"
            animate="visible"
          >
            <motion.div
              className="stat-display"
              variants={getAnimationVariants(heroText)}
            >
              <motion.div
                className="stat-number"
                variants={getAnimationVariants(heroText)}
              >
                {currentVideo.statistic}
              </motion.div>
              <motion.div
                className="stat-type"
                variants={getAnimationVariants(heroText)}
              >
                {currentVideo.statType}
              </motion.div>
              <motion.div
                className="stat-metric"
                variants={getAnimationVariants(heroText)}
              >
                {currentVideo.statMetric}
              </motion.div>
            </motion.div>
          </motion.div>
        </div>

        {/* Navigation Dots - Bottom Right */}
        <div className="absolute bottom-8 right-8 z-20 flex space-x-3">
          {videoData.map((_, index) => (
            <button
              key={index}
              onClick={() => handleDotClick(index)}
              className={`nav-dot ${
                index === currentSlide ? "active" : "inactive"
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>

        {/* Progress Bar */}
        <div className="progress-bar-container">
          <div className="progress-bar-track">
            <div
              className="progress-bar-fill"
              style={{
                width: `${((currentSlide + 1) / videoData.length) * 100}%`,
              }}
            />
          </div>
        </div>

        {/* Mobile Statistics Display */}
        <div className="absolute top-8 right-8 z-20 md:hidden text-right" style={{ color: 'var(--color-white)' }}>
          <div className="text-4xl font-light opacity-90">{currentVideo.statistic}</div>
          <div className="text-sm font-medium opacity-80">{currentVideo.statType}</div>
          <div className="text-xs opacity-70">{currentVideo.statMetric}</div>
        </div>


      </motion.section>

      {/* Section 2: Product Overview */}
      <ProductOverviewSection />

      {/* Section 3: Features */}
      <FeaturesSection />

      {/* Section 4: How It Works */}
      <HowItWorksSection />

      {/* Section 5: Three Pillars of Health */}
      <ThreePillarsSection />

      {/* Section 6: The Science - Diamond Section */}
      <DiamondSection />

      {/* Section 7: Product Features - Interactive Image Section */}
      <InteractiveImageSection />

      {/* Enhanced Our Team Section */}
      <TeamSection />

      {/* Section 8: Sparkles Waitlist */}
      <SparklesWaitlistSection />
    </div>
  )
}
