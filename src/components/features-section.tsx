"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { getSectionContent, ProblemSolutionContent } from "@/lib/content"
import { useInViewAnimation } from "@/lib/hooks/useInViewAnimation"
import { fadeInUp, staggerContainer, getAnimationVariants } from "@/lib/animations"
import { AlertTriangle, Zap, TrendingUp } from "lucide-react"

export function FeaturesSection() {
  const content = getSectionContent(3) as ProblemSolutionContent
  const { ref, isInView } = useInViewAnimation({ threshold: 0.1 })
  const [hoveredCard, setHoveredCard] = useState<number | null>(null)

  // Card data structure with appropriate icons and content
  const cards = [
    {
      id: 0,
      title: "Cellular Degeneration",
      description: content.problem,
      icon: AlertTriangle,
      gradient: "from-red-400 to-orange-500",
      glowColor: "rgba(239, 68, 68, 0.3)",
      bgGradient: "from-red-50/80 to-orange-50/80"
    },
    {
      id: 1,
      title: "Mitochondrial Revitalization",
      description: content.solution,
      icon: Zap,
      gradient: "from-blue-400 to-indigo-500",
      glowColor: "rgba(59, 130, 246, 0.3)",
      bgGradient: "from-blue-50/80 to-indigo-50/80"
    },
    {
      id: 2,
      title: "Health Benefits",
      description: content.benefits,
      icon: TrendingUp,
      gradient: "from-emerald-400 to-teal-500",
      glowColor: "rgba(16, 185, 129, 0.3)",
      bgGradient: "from-emerald-50/80 to-teal-50/80"
    }
  ]

  return (
    <section
      ref={ref}
      className="py-20 px-8 md:px-16 lg:px-24 relative overflow-hidden min-h-screen flex items-center"
      style={{
        background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%)'
      }}
    >
      {/* Floating Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute -top-40 -right-40 w-96 h-96 bg-blue-200/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.1, 0.2, 0.1],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute -bottom-40 -left-40 w-96 h-96 bg-purple-200/10 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.2, 0.1, 0.2],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Floating particles */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-gray-300/20 rounded-full"
            style={{
              left: `${20 + i * 15}%`,
              top: `${30 + (i % 3) * 20}%`,
            }}
            animate={{
              y: [-20, 20, -20],
              opacity: [0.2, 0.5, 0.2],
            }}
            transition={{
              duration: 4 + i * 0.5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 0.3,
            }}
          />
        ))}
      </div>

      <motion.div
        className="max-w-7xl mx-auto w-full relative z-10"
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        variants={staggerContainer}
      >
        <motion.div
          className="text-center mb-20"
          variants={getAnimationVariants(fadeInUp)}
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-transparent">
            {content.title}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Understanding the cellular aging process and how revolutionary technology can reverse its effects
          </p>

          {/* Decorative line */}
          <motion.div
            className="w-24 h-1 bg-gradient-to-r from-blue-400 to-indigo-500 mx-auto mt-8 rounded-full"
            variants={getAnimationVariants(fadeInUp)}
            transition={{ delay: 0.2 }}
          />
        </motion.div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12"
          variants={staggerContainer}
        >
          {cards.map((card, index) => (
            <motion.div
              key={card.id}
              className="group relative"
              variants={getAnimationVariants(fadeInUp)}
              transition={{ delay: index * 0.2 }}
              onHoverStart={() => setHoveredCard(index)}
              onHoverEnd={() => setHoveredCard(null)}
              whileHover={{ scale: 1.02, y: -8 }}
            >
              {/* Floating glow effect */}
              <motion.div
                className={`absolute -inset-4 bg-gradient-to-br ${card.gradient} rounded-3xl opacity-0 blur-xl`}
                animate={{
                  opacity: hoveredCard === index ? 0.15 : 0,
                  scale: hoveredCard === index ? 1.1 : 1,
                }}
                transition={{ duration: 0.3 }}
              />

              {/* Main card with fixed dimensions */}
              <div className="relative bg-white/90 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/60 overflow-hidden w-full h-[420px] flex flex-col">
                {/* Gradient overlay */}
                <div className={`absolute inset-0 bg-gradient-to-br ${card.bgGradient} opacity-0 transition-opacity duration-500 ${hoveredCard === index ? 'opacity-100' : ''}`}></div>

                {/* Subtle border glow effect */}
                <div className={`absolute inset-0 rounded-3xl transition-all duration-500 ${hoveredCard === index ? `shadow-lg` : ''}`} style={{ boxShadow: hoveredCard === index ? `0 10px 25px ${card.glowColor}` : '' }}></div>

                <div className="relative z-10 flex flex-col items-center text-center space-y-6 flex-1 justify-center">
                  {/* Enhanced Icon */}
                  <div className="relative">
                    <motion.div
                      className={`relative w-20 h-20 rounded-2xl bg-gradient-to-br ${card.gradient} shadow-xl flex items-center justify-center`}
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      transition={{ type: "spring", stiffness: 400 }}
                    >
                      <card.icon className="w-10 h-10 text-white" />
                    </motion.div>

                    {/* Pulse ring animation */}
                    <motion.div
                      className={`absolute inset-0 rounded-2xl border-2 opacity-0`}
                      style={{ borderColor: card.gradient.includes('red') ? '#ef4444' : card.gradient.includes('blue') ? '#3b82f6' : '#10b981' }}
                      animate={{
                        scale: hoveredCard === index ? [1, 1.3, 1] : 1,
                        opacity: hoveredCard === index ? [0, 0.4, 0] : 0,
                      }}
                      transition={{
                        duration: 2,
                        repeat: hoveredCard === index ? Infinity : 0,
                        ease: "easeInOut"
                      }}
                    />
                  </div>

                  {/* Enhanced Title */}
                  <motion.h3
                    className="text-2xl md:text-3xl font-bold text-gray-900 leading-tight"
                    animate={{
                      color: hoveredCard === index ? "#1f2937" : "#111827",
                    }}
                    transition={{ duration: 0.3 }}
                  >
                    {card.title}
                  </motion.h3>

                  {/* Enhanced Description */}
                  <motion.p
                    className="text-sm md:text-base leading-relaxed text-gray-600 max-w-sm mx-auto"
                    animate={{
                      color: hoveredCard === index ? "#4b5563" : "#6b7280",
                    }}
                    transition={{ duration: 0.3 }}
                  >
                    {card.description}
                  </motion.p>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </motion.div>
    </section>
  )
}
