import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header } from '@/components/ui/card'
import { Settings2, <PERSON><PERSON><PERSON>, Zap } from 'lucide-react'
import { ReactNode } from 'react'

export function FeaturesSection() {
    return (
        <section className="py-16 md:py-32">
            <div className="@container mx-auto max-w-5xl px-6">
                <div className="text-center">
                    <h2 className="text-balance text-4xl font-semibold lg:text-5xl">Built to Transform Your Health</h2>
                    <p className="mt-4">Experience the future of respiratory optimization with cutting-edge technology.</p>
                </div>
                <div className="@min-4xl:max-w-full @min-4xl:grid-cols-3 mx-auto mt-8 grid max-w-sm gap-6 *:text-center md:mt-16">
                    <Card className="group border-0 bg-muted shadow-none">
                        <CardHeader className="pb-3">
                            <CardDecorator>
                                <Zap className="size-6" aria-hidden />
                            </CardDecorator>

                            <h3 className="mt-6 font-medium">Precision Engineering</h3>
                        </CardHeader>

                        <CardContent>
                            <p className="text-sm">Advanced oxygen optimization technology that adapts to your unique breathing patterns for maximum efficiency.</p>
                        </CardContent>
                    </Card>

                    <Card className="group border-0 bg-muted shadow-none">
                        <CardHeader className="pb-3">
                            <CardDecorator>
                                <Settings2 className="size-6" aria-hidden />
                            </CardDecorator>

                            <h3 className="mt-6 font-medium">Complete Control</h3>
                        </CardHeader>

                        <CardContent>
                            <p className="text-sm">Monitor and track your progress in real-time with our companion app, giving you full control over your health journey.</p>
                        </CardContent>
                    </Card>

                    <Card className="group border-0 bg-muted shadow-none">
                        <CardHeader className="pb-3">
                            <CardDecorator>
                                <Sparkles className="size-6" aria-hidden />
                            </CardDecorator>

                            <h3 className="mt-6 font-medium">Scientifically Proven</h3>
                        </CardHeader>

                        <CardContent>
                            <p className="text-sm">Backed by extensive research and clinical studies to deliver measurable improvements in your cardiovascular health.</p>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </section>
    )
}

const CardDecorator = ({ children }: { children: ReactNode }) => (
    <div aria-hidden className="relative mx-auto size-36 [mask-image:radial-gradient(ellipse_50%_50%_at_50%_50%,#000_70%,transparent_100%)]">
        <div className="absolute inset-0 [--border:black] dark:[--border:white] bg-[linear-gradient(to_right,var(--border)_1px,transparent_1px),linear-gradient(to_bottom,var(--border)_1px,transparent_1px)] bg-[size:24px_24px] opacity-10"/>
        <div className="bg-background absolute inset-0 m-auto flex size-12 items-center justify-center border-t border-l">{children}</div>
    </div>
)
