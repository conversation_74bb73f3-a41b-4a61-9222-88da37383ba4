"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { SparklesCore } from "@/components/ui/sparkles"
import { getSectionContent, WaitlistCTAContent } from "@/lib/content"

export function SparklesWaitlistSection() {
  const content = getSectionContent(8) as WaitlistCTAContent
  const [email, setEmail] = useState("")
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (email) {
      // Handle email submission here
      console.log("Email submitted:", email)
      setIsSubmitted(true)
      setTimeout(() => {
        setIsSubmitted(false)
        setEmail("")
      }, 3000)
    }
  }

  return (
    <section className="relative min-h-screen w-full bg-black flex flex-col items-center justify-center overflow-hidden">
      {/* Sparkles Background */}
      <div className="w-full absolute inset-0 h-screen">
        <SparklesCore
          id="tsparticleswaitlist"
          background="transparent"
          minSize={0.6}
          maxSize={1.4}
          particleDensity={100}
          className="w-full h-full"
          particleColor="#FFFFFF"
          speed={1}
        />
      </div>

      {/* Content */}
      <div className="relative z-20 max-w-4xl mx-auto w-full text-center px-8 md:px-16 lg:px-24">
        <h2 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-b from-neutral-50 to-neutral-400">
          Join Waitlist
        </h2>
        
        <p className="text-lg md:text-xl mb-12 max-w-2xl mx-auto text-neutral-300">
          {content.subheadline}
        </p>

        {!isSubmitted ? (
          <form onSubmit={handleSubmit} className="max-w-md mx-auto mb-8">
            <div className="flex flex-col sm:flex-row gap-4">
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder={content.emailPlaceholder}
                className="flex-1 px-4 py-3 rounded-lg border-2 border-transparent bg-white/10 backdrop-blur-sm text-white placeholder-white/70 focus:outline-none focus:border-white/50 transition-all"
                required
              />
              <Button 
                type="submit" 
                size="lg" 
                className="bg-white text-black hover:bg-gray-100 font-semibold px-8 py-3 whitespace-nowrap transition-all duration-300 hover:scale-105"
              >
                {content.button}
              </Button>
            </div>
          </form>
        ) : (
          <div className="max-w-md mx-auto mb-8">
            <div className="success-message p-8 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg">
              <div className="success-icon mb-4 flex justify-center">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-green-400">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2 text-white">
                {content.successTitle}
              </h3>
              <p className="text-white/90">
                {content.successMessage}
              </p>
            </div>
          </div>
        )}

        <p className="text-sm text-white/80">
          {content.text}
        </p>
      </div>
    </section>
  )
}
