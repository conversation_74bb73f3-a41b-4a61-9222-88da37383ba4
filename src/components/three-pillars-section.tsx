"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { getSectionContent, ThreePillarsContent } from "@/lib/content"
import { useInViewAnimation } from "@/lib/hooks/useInViewAnimation"
import { fadeInUp, staggerContainer, getAnimationVariants } from "@/lib/animations"

export function ThreePillarsSection() {
  const content = getSectionContent(5) as ThreePillarsContent
  const { ref, isInView } = useInViewAnimation({ threshold: 0.1 })
  const [hoveredCard, setHoveredCard] = useState<number | null>(null)

  // Enhanced chart data with more realistic health metrics
  const chartData = [
    {
      name: "Cardiovascular Health",
      metrics: [
        { label: "Heart Rate", value: "72", unit: "BPM", trend: "optimal" },
        { label: "VO2 Max", value: "45", unit: "ml/kg/min", trend: "improving" },
        { label: "Blood Pressure", value: "120/80", unit: "mmHg", trend: "normal" }
      ],
      chartBars: [65, 78, 45, 82, 70, 88, 75],
      color: "#ef4444",
      gradient: "from-red-500 to-pink-500"
    },
    {
      name: "Metabolic Health",
      metrics: [
        { label: "Glucose", value: "95", unit: "mg/dL", trend: "stable" },
        { label: "Insulin", value: "8.2", unit: "μU/mL", trend: "optimal" },
        { label: "HbA1c", value: "5.4", unit: "%", trend: "excellent" }
      ],
      chartBars: [58, 71, 43, 76, 62, 85, 69],
      color: "#10b981",
      gradient: "from-emerald-500 to-teal-500"
    },
    {
      name: "Cognitive Performance",
      metrics: [
        { label: "Focus Score", value: "92", unit: "%", trend: "high" },
        { label: "Memory", value: "88", unit: "%", trend: "improving" },
        { label: "Processing", value: "95", unit: "%", trend: "excellent" }
      ],
      chartBars: [72, 58, 81, 45, 69, 78, 85],
      color: "#3b82f6",
      gradient: "from-blue-500 to-indigo-500"
    }
  ]

  return (
    <section
      ref={ref}
      className="py-16 md:py-32 px-8 md:px-16 lg:px-24"
      style={{ backgroundColor: 'var(--color-white)' }}
    >

      <motion.div
        className="max-w-7xl mx-auto w-full"
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        variants={staggerContainer}
      >
        <motion.div
          className="text-center mb-12"
          variants={getAnimationVariants(fadeInUp)}
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6" style={{ color: 'var(--color-gray-900)' }}>
            {content.title}
          </h2>
          <p className="text-xl max-w-3xl mx-auto leading-relaxed" style={{ color: 'var(--color-gray-600)' }}>
            Experience comprehensive health monitoring with real-time insights across three critical areas of wellness
          </p>
        </motion.div>

        <div className="space-y-12 md:space-y-16">
          {content.pillars.map((pillar, index) => (
            <motion.div
              key={index}
              className={`flex flex-col ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} items-center gap-12 lg:gap-20`}
              variants={getAnimationVariants(fadeInUp)}
              transition={{ delay: index * 0.2 }}
            >
              {/* Enhanced Content Card */}
              <motion.div
                className="flex-1 relative"
                onHoverStart={() => setHoveredCard(index)}
                onHoverEnd={() => setHoveredCard(null)}
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300, damping: 30 }}
              >
                <div className="relative bg-white/90 backdrop-blur-md rounded-3xl p-8 lg:p-10 shadow-2xl border border-white/60 overflow-hidden group">
                  {/* Enhanced gradient overlay with better transitions */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${chartData[index].gradient} opacity-0 transition-all duration-700 ease-out ${hoveredCard === index ? 'opacity-10' : ''}`}></div>

                  {/* Subtle border glow effect */}
                  <div className={`absolute inset-0 rounded-3xl transition-all duration-500 ${hoveredCard === index ? `shadow-lg shadow-${chartData[index].color}/20` : ''}`}></div>

                  <div className="relative z-10">
                    <div className="flex items-center gap-4 mb-6">
                      <motion.div
                        className={`p-4 rounded-2xl bg-gradient-to-br ${chartData[index].gradient} shadow-lg`}
                        whileHover={{ rotate: 5, scale: 1.1 }}
                        transition={{ type: "spring", stiffness: 400 }}
                      >
                        {index === 0 && (
                          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.29 1.51 4.04 3 5.5l7 7Z" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        )}
                        {index === 1 && (
                          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        )}
                        {index === 2 && (
                          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        )}
                      </motion.div>
                      <div>
                        <h3 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-2">
                          {pillar.title}
                        </h3>
                        <div className={`h-1 w-20 bg-gradient-to-r ${chartData[index].gradient} rounded-full`}></div>
                      </div>
                    </div>

                    <p className="text-lg lg:text-xl leading-relaxed text-gray-700 mb-8">
                      {pillar.description}
                    </p>

                    {/* Enhanced Metrics Display */}
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                      {chartData[index].metrics.map((metric, metricIndex) => (
                        <motion.div
                          key={metricIndex}
                          className="bg-muted rounded-lg p-4 border-0 shadow-none"
                          whileHover={{ y: -2 }}
                          transition={{ type: "spring", stiffness: 400 }}
                        >
                          <div className="text-sm font-medium text-gray-600 mb-1">{metric.label}</div>
                          <div className="text-2xl font-bold text-gray-900 mb-1">
                            {metric.value}
                            <span className="text-sm font-normal text-gray-500 ml-1">{metric.unit}</span>
                          </div>
                          <div className={`text-xs px-2 py-1 rounded-full inline-block ${
                            metric.trend === 'optimal' || metric.trend === 'excellent' ? 'bg-green-100 text-green-700' :
                            metric.trend === 'improving' || metric.trend === 'high' ? 'bg-blue-100 text-blue-700' :
                            'bg-gray-100 text-gray-700'
                          }`}>
                            {metric.trend}
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Enhanced Interactive Device Mockup */}
              <motion.div
                className="flex-1 relative"
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 300, damping: 30 }}
              >
                <div className="relative">
                  {/* Floating background elements */}
                  <motion.div
                    className={`absolute -inset-4 bg-gradient-to-br ${chartData[index].gradient} rounded-3xl opacity-20 blur-xl`}
                    animate={{
                      scale: hoveredCard === index ? 1.1 : 1,
                      opacity: hoveredCard === index ? 0.3 : 0.2,
                    }}
                    transition={{ duration: 0.3 }}
                  />

                  {/* Enhanced Device Container */}
                  <div className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 rounded-3xl p-6 shadow-2xl border border-gray-700/50 overflow-hidden group">
                    {/* Enhanced lighting effect */}
                    <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-transparent rounded-3xl"></div>

                    {/* Device Screen with enhanced shadows */}
                    <div className="bg-white rounded-2xl p-6 shadow-inner relative overflow-hidden border border-gray-100/50">
                      {/* Screen reflection effect */}
                      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-purple-50/20 rounded-2xl pointer-events-none"></div>
                      {/* Screen Header */}
                      <div className="flex items-center justify-between mb-6">
                        <div className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full bg-gradient-to-r ${chartData[index].gradient}`}></div>
                          <h4 className="text-lg font-bold text-gray-900">
                            {pillar.title.split(' ')[0]} Monitor
                          </h4>
                        </div>
                        <div className="flex gap-1">
                          <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                          <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                          <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                        </div>
                      </div>

                      {/* Interactive Chart */}
                      <div className="mb-6">
                        <div className="flex items-end justify-center gap-2 h-32 mb-4">
                          {chartData[index].chartBars.map((height, i) => (
                            <motion.div
                              key={i}
                              className={`w-6 bg-gradient-to-t ${chartData[index].gradient} rounded-t-lg relative overflow-hidden`}
                              style={{ height: `${height}%` }}
                              initial={{ height: 0, opacity: 0 }}
                              animate={{
                                height: isInView ? `${height}%` : 0,
                                opacity: isInView ? 1 : 0
                              }}
                              transition={{
                                delay: index * 0.3 + i * 0.1,
                                duration: 0.6,
                                ease: "easeOut"
                              }}
                              whileHover={{
                                scale: 1.1,
                                filter: "brightness(1.2)"
                              }}
                            >
                              {/* Shimmer effect */}
                              <motion.div
                                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                                animate={{
                                  x: ["-100%", "100%"]
                                }}
                                transition={{
                                  duration: 2,
                                  repeat: Infinity,
                                  repeatDelay: 3,
                                  ease: "linear"
                                }}
                              />
                            </motion.div>
                          ))}
                        </div>

                        {/* Chart Labels */}
                        <div className="flex justify-between text-xs text-gray-500">
                          <span>Mon</span>
                          <span>Tue</span>
                          <span>Wed</span>
                          <span>Thu</span>
                          <span>Fri</span>
                          <span>Sat</span>
                          <span>Sun</span>
                        </div>
                      </div>

                      {/* Stats Display */}
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center">
                          <motion.div
                            className="text-3xl font-bold text-gray-900 mb-1"
                            animate={{
                              scale: hoveredCard === index ? 1.1 : 1,
                            }}
                            transition={{ duration: 0.2 }}
                          >
                            {chartData[index].metrics[0].value}
                          </motion.div>
                          <div className="text-sm text-gray-600">{chartData[index].metrics[0].label}</div>
                        </div>
                        <div className="text-center">
                          <motion.div
                            className="text-3xl font-bold text-gray-900 mb-1"
                            animate={{
                              scale: hoveredCard === index ? 1.1 : 1,
                            }}
                            transition={{ duration: 0.2, delay: 0.1 }}
                          >
                            {chartData[index].metrics[1].value}
                          </motion.div>
                          <div className="text-sm text-gray-600">{chartData[index].metrics[1].label}</div>
                        </div>
                      </div>

                      {/* Pulse Animation */}
                      <motion.div
                        className={`absolute top-4 right-4 w-4 h-4 bg-gradient-to-r ${chartData[index].gradient} rounded-full`}
                        animate={{
                          scale: [1, 1.2, 1],
                          opacity: [1, 0.7, 1],
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      />
                    </div>

                    {/* Device Caption */}
                    <motion.p
                      className="text-sm mt-4 text-center text-gray-400"
                      animate={{
                        opacity: hoveredCard === index ? 1 : 0.7,
                      }}
                      transition={{ duration: 0.3 }}
                    >
                      {pillar.mockup}
                    </motion.p>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </section>
  )
}
