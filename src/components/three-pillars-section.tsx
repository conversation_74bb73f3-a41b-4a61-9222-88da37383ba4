"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { getSectionContent, ThreePillarsContent } from "@/lib/content"
import { useInViewAnimation } from "@/lib/hooks/useInViewAnimation"
import { fadeInUp, staggerContainer, getAnimationVariants } from "@/lib/animations"

export function ThreePillarsSection() {
  const content = getSectionContent(5) as ThreePillarsContent
  const { ref, isInView } = useInViewAnimation({ threshold: 0.1 })

  // Enhanced chart data with consistent brand colors
  const chartData = [
    {
      name: "Cardiovascular Health",
      metrics: [
        { label: "Heart Rate", value: "72", unit: "BPM", trend: "optimal" },
        { label: "VO2 Max", value: "45", unit: "ml/kg/min", trend: "improving" },
        { label: "Blood Pressure", value: "120/80", unit: "mmHg", trend: "normal" }
      ],
      chartBars: [65, 78, 45, 82, 70, 88, 75],
      color: "var(--color-brand-blue-600)"
    },
    {
      name: "Metabolic Health",
      metrics: [
        { label: "Glucose", value: "95", unit: "mg/dL", trend: "stable" },
        { label: "Insulin", value: "8.2", unit: "μU/mL", trend: "optimal" },
        { label: "HbA1c", value: "5.4", unit: "%", trend: "excellent" }
      ],
      chartBars: [58, 71, 43, 76, 62, 85, 69],
      color: "var(--color-brand-blue-600)"
    },
    {
      name: "Cognitive Performance",
      metrics: [
        { label: "Focus Score", value: "92", unit: "%", trend: "high" },
        { label: "Memory", value: "88", unit: "%", trend: "improving" },
        { label: "Processing", value: "95", unit: "%", trend: "excellent" }
      ],
      chartBars: [72, 58, 81, 45, 69, 78, 85],
      color: "var(--color-brand-blue-600)"
    }
  ]

  return (
    <section
      ref={ref}
      className="py-16 md:py-32 px-8 md:px-16 lg:px-24"
      style={{ backgroundColor: 'var(--color-white)' }}
    >
      <motion.div
        className="max-w-7xl mx-auto w-full"
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        variants={staggerContainer}
      >
        <motion.div
          className="text-center mb-12"
          variants={getAnimationVariants(fadeInUp)}
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6" style={{ color: 'var(--color-gray-900)' }}>
            {content.title}
          </h2>
          <p className="text-xl max-w-3xl mx-auto leading-relaxed" style={{ color: 'var(--color-gray-600)' }}>
            Experience comprehensive health monitoring with real-time insights across three critical areas of wellness
          </p>
        </motion.div>

        <div className="space-y-12 md:space-y-16">
          {content.pillars.map((pillar, index) => (
            <motion.div
              key={index}
              className={`flex flex-col ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} items-center gap-12 lg:gap-20`}
              variants={getAnimationVariants(fadeInUp)}
            >
              {/* Content Section */}
              <motion.div className="flex-1 space-y-6">
                <div className="flex items-center gap-6">
                  <motion.div
                    className="w-16 h-16 rounded-2xl flex items-center justify-center"
                    style={{ backgroundColor: 'var(--color-brand-blue-600)' }}
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ type: "spring", stiffness: 400, damping: 30 }}
                  >
                    {index === 0 && (
                      <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    )}
                    {index === 1 && (
                      <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M14 9V5a3 3 0 0 0-6 0v4" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M3 9h18l-1.5 12H4.5L3 9z" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    )}
                    {index === 2 && (
                      <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    )}
                  </motion.div>
                  <div>
                    <h3 className="text-3xl lg:text-4xl font-bold mb-2" style={{ color: 'var(--color-gray-900)' }}>
                      {pillar.title}
                    </h3>
                    <div className="h-1 w-20 rounded-full" style={{ backgroundColor: 'var(--color-brand-blue-600)' }}></div>
                  </div>
                </div>

                <p className="text-lg lg:text-xl leading-relaxed mb-8" style={{ color: 'var(--color-gray-700)' }}>
                  {pillar.description}
                </p>

                {/* Simplified Metrics Display */}
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  {chartData[index].metrics.map((metric, metricIndex) => (
                    <motion.div
                      key={metricIndex}
                      className="bg-muted rounded-lg p-4 border-0 shadow-none"
                      whileHover={{ y: -2 }}
                      transition={{ type: "spring", stiffness: 400 }}
                    >
                      <div className="text-sm font-medium mb-1" style={{ color: 'var(--color-gray-600)' }}>{metric.label}</div>
                      <div className="text-2xl font-bold mb-1" style={{ color: 'var(--color-gray-900)' }}>
                        {metric.value}
                        <span className="text-sm font-normal ml-1" style={{ color: 'var(--color-gray-500)' }}>{metric.unit}</span>
                      </div>
                      <div className={`text-xs px-2 py-1 rounded-full inline-block ${
                        metric.trend === 'optimal' || metric.trend === 'excellent' ? 'bg-green-100 text-green-700' :
                        metric.trend === 'improving' || metric.trend === 'high' ? 'bg-blue-100 text-blue-700' :
                        'bg-gray-100 text-gray-700'
                      }`}>
                        {metric.trend}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Simple Progress Chart */}
              <motion.div
                className="flex-1"
                variants={getAnimationVariants(fadeInUp)}
              >
                <div className="bg-white rounded-lg border p-6 shadow-sm">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-semibold" style={{ color: 'var(--color-gray-900)' }}>
                      {pillar.title} Progress
                    </h4>
                    <div className="w-3 h-3 rounded-full" style={{ backgroundColor: chartData[index].color }}></div>
                  </div>
                  
                  <div className="space-y-3">
                    {chartData[index].chartBars.slice(0, 4).map((value, i) => (
                      <div key={i} className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <span style={{ color: 'var(--color-gray-600)' }}>Week {i + 1}</span>
                          <span style={{ color: 'var(--color-gray-900)' }}>{value}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <motion.div
                            className="h-2 rounded-full"
                            style={{ backgroundColor: chartData[index].color }}
                            initial={{ width: 0 }}
                            animate={{ width: isInView ? `${value}%` : 0 }}
                            transition={{ delay: index * 0.2 + i * 0.1, duration: 0.6 }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </section>
  )
}
