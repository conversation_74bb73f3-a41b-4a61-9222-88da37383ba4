"use client"

import { useState } from "react"
import { getSectionContent, ScienceContent } from "@/lib/content"

export function DiamondSection() {
  const [activeSegment, setActiveSegment] = useState<number | null>(null)
  const content = getSectionContent(6) as ScienceContent
  const segments = content.segments

  // Calculate path for each segment (90 degrees each, starting from top)
  const createSegmentPath = (segmentIndex: number) => {
    const startAngle = segmentIndex * 90 - 90 // Start from top (-90°), go clockwise
    const endAngle = startAngle + 90
    const outerRadius = 320
    const innerRadius = 180
    const centerX = 500 // Center of 1000x1000 viewBox
    const centerY = 500

    const startAngleRad = (startAngle * Math.PI) / 180
    const endAngleRad = (endAngle * Math.PI) / 180

    const x1 = centerX + outerRadius * Math.cos(startAngleRad)
    const y1 = centerY + outerRadius * Math.sin(startAngleRad)
    const x2 = centerX + outerRadius * Math.cos(endAngleRad)
    const y2 = centerY + outerRadius * Math.sin(endAngleRad)

    const x3 = centerX + innerRadius * Math.cos(endAngleRad)
    const y3 = centerY + innerRadius * Math.sin(endAngleRad)
    const x4 = centerX + innerRadius * Math.cos(startAngleRad)
    const y4 = centerY + innerRadius * Math.sin(startAngleRad)

    return `M ${x1} ${y1} A ${outerRadius} ${outerRadius} 0 0 1 ${x2} ${y2} L ${x3} ${y3} A ${innerRadius} ${innerRadius} 0 0 0 ${x4} ${y4} Z`
  }

  // Calculate text position for each segment
  const getNumberPosition = (segmentIndex: number) => {
    const angle = segmentIndex * 90 - 45 // Middle of each segment
    const radius = 250 // Middle of the thick ring
    const centerX = 500 // Center of 1000x1000 viewBox
    const centerY = 500
    const angleRad = (angle * Math.PI) / 180
    const x = centerX + radius * Math.cos(angleRad)
    const y = centerY + radius * Math.sin(angleRad)
    return { x, y }
  }

  // Position titles outside the ring segments
  const getExternalTitlePosition = (segmentIndex: number) => {
    const angle = segmentIndex * 90 - 45 // Middle of each segment
    const radius = 420 // Further outside the ring (increased from 380)
    const centerX = 500 // Center of 1000x1000 viewBox
    const centerY = 500
    const angleRad = (angle * Math.PI) / 180
    const x = centerX + radius * Math.cos(angleRad)
    const y = centerY + radius * Math.sin(angleRad)
    return { x, y }
  }

  return (
    <section className="relative min-h-screen w-full overflow-hidden" style={{ backgroundColor: 'var(--color-black)' }}>
      {/* Background Video */}
      <div className="absolute inset-0">
        <div
          className="h-full w-full bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: "url('/placeholder.svg?height=1080&width=1920&text=Background+Video')",
          }}
        />
        <div className="absolute inset-0 video-overlay-light" />
      </div>

      {/* Content */}
      <div className="relative z-10 flex h-full items-center px-4 md:px-8 lg:px-16 xl:px-24 py-8 md:py-16">
        <div className="w-full max-w-7xl mx-auto">
          
          {/* Mobile Layout */}
          <div className="block lg:hidden">
            {/* Mobile Card Layout */}
            <div className="w-full mb-8">
              <h2 className="text-2xl font-bold mb-6 text-center" style={{ color: 'var(--color-white)' }}>{content.title}</h2>
              <div className="grid grid-cols-2 gap-4 mb-8">
                {segments.map((segment) => (
                  <button
                    key={segment.number}
                    onClick={() => setActiveSegment(activeSegment === segment.number ? null : segment.number)}
                    className={`p-4 rounded-xl transition-all duration-300 border-2 min-h-[120px] ${
                      activeSegment === segment.number
                        ? "border-white bg-white/20"
                        : "border-white/30 bg-white/10 hover:bg-white/15"
                    }`}
                  >
                    <div className="text-center">
                      <div className={`text-3xl font-bold mb-2 ${
                        activeSegment === segment.number ? "text-white" : "text-white/90"
                      }`}>
                        {segment.number}
                      </div>
                      <div className={`text-sm font-medium leading-tight ${
                        activeSegment === segment.number ? "text-white" : "text-white/80"
                      }`}>
                        {segment.title}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
            
            {/* Mobile Information Panel */}
            <div className="w-full">
              {activeSegment ? (
                <div className="diamond-info-panel">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: 'var(--color-white)' }}>
                      <span className="text-lg font-bold" style={{ color: 'var(--color-black)' }}>{activeSegment}</span>
                    </div>
                    <h3 className="text-xl font-bold">{segments[activeSegment - 1].title}</h3>
                  </div>
                  <p className="text-base leading-relaxed mb-4" style={{ color: 'rgba(255, 255, 255, 0.9)' }}>{segments[activeSegment - 1].description}</p>
                  <div className="space-y-4 mb-6">
                    <div className="flex items-center text-sm" style={{ color: 'rgba(255, 255, 255, 0.7)' }}>
                      <div className="w-2 h-2 rounded-full mr-3" style={{ backgroundColor: 'rgba(255, 255, 255, 0.5)' }}></div>
                      <span>Phase {activeSegment} of 4</span>
                    </div>
                    <div className="w-full rounded-full h-2" style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}>
                      <div
                        className="h-2 rounded-full transition-all duration-500"
                        style={{
                          backgroundColor: 'var(--color-white)',
                          width: `${(activeSegment / 4) * 100}%`
                        }}
                      ></div>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    {segments.map((segment) => (
                      <button
                        key={segment.number}
                        onClick={() => setActiveSegment(segment.number)}
                        className={`p-2 rounded-lg transition-all duration-300 border min-h-[60px] ${
                          activeSegment === segment.number
                            ? "border-white"
                            : "hover:border-white/40"
                        }`}
                        style={{
                          backgroundColor: activeSegment === segment.number
                            ? 'var(--color-white)'
                            : 'rgba(255, 255, 255, 0.1)',
                          color: activeSegment === segment.number
                            ? 'var(--color-black)'
                            : 'var(--color-white)',
                          borderColor: activeSegment === segment.number
                            ? 'var(--color-white)'
                            : 'rgba(255, 255, 255, 0.2)'
                        }}
                      >
                        <div className="text-center">
                          <div className="font-bold text-base mb-1">{segment.number}</div>
                          <div className="text-xs font-medium leading-tight">{segment.title}</div>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="diamond-info-panel-inactive">
                  <h3 className="text-xl font-bold mb-4">{content.inactiveTitle}</h3>
                  <p className="text-base leading-relaxed mb-4" style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                    {content.inactiveDescription}
                  </p>
                  <div className="grid grid-cols-1 gap-3">
                    {segments.map((segment) => (
                      <button
                        key={segment.number}
                        onClick={() => setActiveSegment(segment.number)}
                        className="text-left p-3 rounded-lg transition-colors border min-h-[60px]"
                        style={{
                          backgroundColor: 'rgba(255, 255, 255, 0.1)',
                          borderColor: 'rgba(255, 255, 255, 0.1)'
                        }}
                      >
                        <div className="flex items-center mb-2">
                          <span className="w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold mr-2" style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}>
                            {segment.number}
                          </span>
                          <span className="font-semibold text-sm">{segment.title}</span>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Desktop Layout */}
          <div className="hidden lg:flex lg:items-center lg:justify-between gap-16">
            {/* Desktop Ring */}
            <div className="flex-shrink-0">
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-6 md:mb-8 text-center" style={{ color: 'var(--color-white)' }}>{content.title}</h2>
            <div className="flex justify-center">
              <svg
                width="1000"
                height="1000"
                viewBox="0 0 1000 1000"
                className="w-[300px] h-[300px] sm:w-[400px] sm:h-[400px] md:w-[500px] md:h-[500px] lg:w-[600px] lg:h-[600px] xl:w-[700px] xl:h-[700px]"
              >
              {segments.map((segment, index) => {
                const numberPos = getNumberPosition(index)
                const externalTitlePos = getExternalTitlePosition(index)
                const isActive = activeSegment === segment.number

                return (
                  <g key={segment.number}>
                    {/* Segment Path */}
                    <path
                      d={createSegmentPath(index)}
                      fill={isActive ? "rgba(255, 255, 255, 0.9)" : "rgba(255, 255, 255, 0.15)"}
                      stroke="white"
                      strokeWidth="2"
                      className="diamond-segment cursor-pointer hover:fill-white/30"
                      onClick={() => setActiveSegment(activeSegment === segment.number ? null : segment.number)}
                    />

                    {/* Segment Number */}
                    <text
                      x={numberPos.x}
                      y={numberPos.y}
                      textAnchor="middle"
                      dominantBaseline="middle"
                      className={`text-4xl md:text-5xl font-bold cursor-pointer transition-colors duration-300 ${
                        isActive ? "fill-black" : "fill-white"
                      }`}
                      onClick={() => setActiveSegment(activeSegment === segment.number ? null : segment.number)}
                    >
                      {segment.number}
                    </text>

                    {/* External Phase Title - Outside Ring - Hidden on small screens */}
                    <text
                      x={externalTitlePos.x}
                      y={externalTitlePos.y}
                      textAnchor="middle"
                      dominantBaseline="middle"
                      className={`text-sm md:text-lg lg:text-xl xl:text-2xl font-bold cursor-pointer transition-colors duration-300 hidden md:block ${
                        isActive ? "fill-white" : "fill-white/90"
                      }`}
                      onClick={() => setActiveSegment(activeSegment === segment.number ? null : segment.number)}
                    >
                      {segment.title === "Mitochondrial Genesis" ? (
                        <>
                          <tspan x={externalTitlePos.x} dy="-0.5em">Mitochondrial</tspan>
                          <tspan x={externalTitlePos.x} dy="1em">Genesis</tspan>
                        </>
                      ) : (
                        segment.title
                      )}
                    </text>
                  </g>
                )
              })}

              {/* Center Circle */}
              <circle
                cx="500"
                cy="500"
                r="180"
                fill="rgba(0, 0, 0, 0.3)"
                stroke="white"
                strokeWidth="2"
                opacity="0.5"
              />
            </svg>
            </div>
          </div>

            {/* Desktop Information Panel */}
            <div className="flex-1 max-w-lg">
            {activeSegment ? (
              <div className="diamond-info-panel">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 md:w-12 md:h-12 rounded-full flex items-center justify-center mr-3 md:mr-4" style={{ backgroundColor: 'var(--color-white)' }}>
                    <span className="text-lg md:text-2xl font-bold" style={{ color: 'var(--color-black)' }}>{activeSegment}</span>
                  </div>
                  <h3 className="text-xl md:text-2xl lg:text-3xl font-bold">{segments[activeSegment - 1].title}</h3>
                </div>
                <p className="text-base md:text-lg leading-relaxed mb-4 md:mb-6" style={{ color: 'rgba(255, 255, 255, 0.9)' }}>{segments[activeSegment - 1].description}</p>
                <div className="space-y-4 mb-6">
                  <div className="flex items-center text-sm" style={{ color: 'rgba(255, 255, 255, 0.7)' }}>
                    <div className="w-2 h-2 rounded-full mr-3" style={{ backgroundColor: 'rgba(255, 255, 255, 0.5)' }}></div>
                    <span>Phase {activeSegment} of 4</span>
                  </div>
                  <div className="w-full rounded-full h-2" style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}>
                    <div
                      className="h-2 rounded-full transition-all duration-500"
                      style={{
                        backgroundColor: 'var(--color-white)',
                        width: `${(activeSegment / 4) * 100}%`
                      }}
                    ></div>
                  </div>
                </div>
                {/* Navigation Buttons - Always Visible */}
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
                  {segments.map((segment) => (
                    <button
                      key={segment.number}
                      onClick={() => setActiveSegment(segment.number)}
                      className={`p-2 md:p-3 rounded-lg transition-all duration-300 border min-h-[60px] md:min-h-[70px] ${
                        activeSegment === segment.number
                          ? "border-white"
                          : "hover:border-white/40"
                      }`}
                      style={{
                        backgroundColor: activeSegment === segment.number
                          ? 'var(--color-white)'
                          : 'rgba(255, 255, 255, 0.1)',
                        color: activeSegment === segment.number
                          ? 'var(--color-black)'
                          : 'var(--color-white)',
                        borderColor: activeSegment === segment.number
                          ? 'var(--color-white)'
                          : 'rgba(255, 255, 255, 0.2)'
                      }}
                      onMouseEnter={(e) => {
                        if (activeSegment !== segment.number) {
                          e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.2)'
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (activeSegment !== segment.number) {
                          e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)'
                        }
                      }}
                    >
                      <div className="text-center">
                        <div className="font-bold text-base md:text-lg mb-1">{segment.number}</div>
                        <div className="text-xs md:text-sm font-medium leading-tight">{segment.title}</div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            ) : (
              <div className="diamond-info-panel-inactive">
                <h3 className="text-xl md:text-2xl lg:text-3xl font-bold mb-4">{content.inactiveTitle}</h3>
                <p className="text-base md:text-lg leading-relaxed mb-4 md:mb-6" style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                  {content.inactiveDescription}
                </p>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4">
                  {segments.map((segment) => (
                    <button
                      key={segment.number}
                      onClick={() => setActiveSegment(segment.number)}
                      className="text-left p-3 md:p-4 rounded-lg transition-colors border min-h-[60px]"
                      style={{
                        backgroundColor: 'rgba(255, 255, 255, 0.1)',
                        borderColor: 'rgba(255, 255, 255, 0.1)'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.2)'
                        e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.3)'
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)'
                        e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)'
                      }}
                    >
                      <div className="flex items-center mb-2">
                        <span className="w-6 h-6 md:w-8 md:h-8 rounded-full flex items-center justify-center text-xs md:text-sm font-bold mr-2 md:mr-3" style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}>
                          {segment.number}
                        </span>
                        <span className="font-semibold text-sm md:text-base">{segment.title}</span>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
} 